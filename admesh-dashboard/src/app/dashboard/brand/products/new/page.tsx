"use client";

import { useState, useEffect } from "react";
import { useRouter } from "next/navigation";
import { useAuth } from "@/hooks/use-auth";
import { useSubscription } from "@/hooks/use-subscription";
import { Input } from "@/components/ui/input";
import { Textarea } from "@/components/ui/textarea";
import { Button } from "@/components/ui/button";
import { Label } from "@/components/ui/label";
import { Card, CardContent, CardHeader, CardTitle, CardFooter, CardDescription } from "@/components/ui/card";
import { ArrowLeft, Loader2, AlertCircle, ArrowUpCircle } from "lucide-react";
import { toast } from "sonner";
import { motion } from "framer-motion";

// Animated container for transitions
const AnimatedContainer = ({ children }: { children: React.ReactNode }) => (
  <motion.div
    initial={{ opacity: 0, y: 20 }}
    animate={{ opacity: 1, y: 0 }}
    exit={{ opacity: 0, y: -20 }}
    transition={{ duration: 0.4, ease: "easeOut" }}
    className="w-full"
  >
    {children}
  </motion.div>
);

// Animated form field
const FormField = ({ children }: { children: React.ReactNode }) => (
  <motion.div
    initial={{ opacity: 0, y: 10 }}
    animate={{ opacity: 1, y: 0 }}
    transition={{ duration: 0.3 }}
    className="space-y-2 w-full"
  >
    {children}
  </motion.div>
);

export default function NewProductPage() {
  const router = useRouter();
  const { user } = useAuth();
  const { plan, loading: loadingSubscription } = useSubscription();
  const [loading, setLoading] = useState(false);
  const [step] = useState(1);
  const [fetchingWebsiteInfo, setFetchingWebsiteInfo] = useState(false);
  const [websiteError, setWebsiteError] = useState("");
  const [showAllFields, setShowAllFields] = useState(false);
  // const [productCount, setProductCount] = useState(0); // Unused variable
  const [productLimitReached, setProductLimitReached] = useState(false);
  const [checkingProducts, setCheckingProducts] = useState(true);

  // Check if user has reached their product limit based on subscription
  useEffect(() => {
    const checkProductLimit = async () => {
      if (!user || loadingSubscription) return;

      try {
        setCheckingProducts(true);
        const token = await user.getIdToken();
        const response = await fetch(`${process.env.NEXT_PUBLIC_API_BASE_URL}/products/brand/all`, {
          headers: {
            Authorization: `Bearer ${token}`,
          },
        });

        if (response.ok) {
          const data = await response.json();
          const currentProductCount = data.products ? data.products.length : 0;
          // setProductCount(currentProductCount); // Unused variable

          // Check if user has reached their product limit
          const productLimit = plan?.product_listings_limit || 1; // Default to 1 if plan not loaded
          const isUnlimited = productLimit === -1 || productLimit === 0;

          if (!isUnlimited && currentProductCount >= productLimit) {
            setProductLimitReached(true);
            toast.error(`You've reached your product limit (${productLimit}). Please upgrade your subscription to add more products.`);
            setTimeout(() => {
              router.push("/dashboard/brand/subscription");
            }, 3000);
          }
        }
      } catch (error) {
        console.error("Error checking product limits:", error);
      } finally {
        setCheckingProducts(false);
      }
    };

    checkProductLimit();
  }, [user, router, plan, loadingSubscription]);

  // Product form state
  const [product, setProduct] = useState({
    website: "",
    title: "",
    url: "",
    description: "",
    categories: [] as string[],
    keywords: [] as string[],
    pricing_url: "",
    audience_segment: "",
    integration_list: [] as string[],
  });

  // Form validation
  const [errors, setErrors] = useState<Record<string, string>>({});

  // Handle form field changes
  const handleChange = (field: string, value: string | boolean | string[]) => {
    setProduct(prev => ({ ...prev, [field]: value }));
    // Clear error for this field if it exists
    if (errors[field]) {
      setErrors(prev => {
        const newErrors = { ...prev };
        delete newErrors[field];
        return newErrors;
      });
    }
  };

  // Fetch website information
  const fetchWebsiteInfo = async () => {
    if (!user) return;
    if (!product.website) {
      toast.error("Please enter a website URL");
      return;
    }

    setFetchingWebsiteInfo(true);
    setWebsiteError("");

    try {
      const token = await user.getIdToken();
      const res = await fetch(`${process.env.NEXT_PUBLIC_API_BASE_URL}/brands/api/onboard/intel`, {
        method: "POST",
        headers: {
          "Content-Type": "application/json",
          Authorization: `Bearer ${token}`,
        },
        body: JSON.stringify({ website: product.website }),
      });

      if (!res.ok) throw new Error("Failed to fetch website information");

      const data = await res.json();

      // Populate product state with fetched data
      setProduct(prev => ({
        ...prev,
        title: data.title || prev.title,
        url: data.url || product.website, // Use entered website as fallback
        description: data.description || prev.description,
        categories: Array.isArray(data.categories) ? data.categories : prev.categories,
        keywords: Array.isArray(data.keywords) ? data.keywords : prev.keywords,
        pricing_url: data.pricing_url || prev.pricing_url,
        audience_segment: data.audience_segment || prev.audience_segment,
        integration_list: Array.isArray(data.integration_list) ? data.integration_list : prev.integration_list,
      }));

      setShowAllFields(true);
      toast.success("Product info loaded!");
    } catch (err) {
      console.error("❌ Error fetching website info:", err);
      setWebsiteError("Could not fetch information for this website. Please fill in the details manually.");
      setShowAllFields(true);
    } finally {
      setFetchingWebsiteInfo(false);
    }
  };

  // Validate form before submission
  const validateForm = () => {
    const newErrors: Record<string, string> = {};

    if (!product.title) newErrors.title = "Product title is required";
    if (!product.url) newErrors.url = "Product URL is required";
    if (!product.description) newErrors.description = "Description is required";
    if (!product.categories || product.categories.length === 0) newErrors.categories = "At least one category is required";

    setErrors(newErrors);
    return Object.keys(newErrors).length === 0;
  };

  // Submit the product to the API
  const handleSubmit = async () => {
    if (!user) return;
    if (!validateForm()) {
      toast.error("Please fill in all required fields");
      return;
    }

    setLoading(true);

    try {
      const token = await user.getIdToken();

      const res = await fetch(`${process.env.NEXT_PUBLIC_API_BASE_URL}/products/create`, {
        method: "POST",
        headers: {
          "Content-Type": "application/json",
          Authorization: `Bearer ${token}`,
        },
        body: JSON.stringify({
          title: product.title,
          url: product.url,
          description: product.description,
          categories: product.categories,
          keywords: product.keywords,
          pricing_url: product.pricing_url || null,
          audience_segment: product.audience_segment || null,
          integration_list: product.integration_list,
          status: "active", // Always set status to active by default
          confidence_score: 50.0,
        }),
      });

      if (!res.ok) {
        const errorData = await res.json();
        throw new Error(errorData.detail || "Failed to create product");
      }

      toast.success("Product created successfully!");
      router.push("/dashboard/brand/products");
    } catch (err) {
      console.error("❌ Error calling backend:", err);
      toast.error(err instanceof Error ? err.message : "Failed to create product");
    } finally {
      setLoading(false);
    }
  };

  // Show loading state while checking for existing products
  if (checkingProducts) {
    return (
      <div className="max-w-3xl mx-auto py-8 px-4 md:px-0">
        <Button variant="link" onClick={() => router.back()} className="mb-4 p-0">
          <ArrowLeft className="h-4 w-4 mr-2" />
          Back to Products
        </Button>

        <div className="flex items-center justify-center h-64">
          <div className="text-center">
            <Loader2 className="h-8 w-8 animate-spin mx-auto mb-4 text-primary" />
            <p className="text-muted-foreground">Checking your existing products...</p>
          </div>
        </div>
      </div>
    );
  }

  // Show message if user has reached their product limit
  if (productLimitReached) {
    return (
      <div className="max-w-3xl mx-auto py-8 px-4 md:px-0">
        <Button variant="link" onClick={() => router.back()} className="mb-4 p-0">
          <ArrowLeft className="h-4 w-4 mr-2" />
          Back to Products
        </Button>

        <Card className="shadow-md">
          <CardContent className="pt-6">
            <div className="flex flex-col items-center justify-center py-8 text-center">
              <AlertCircle className="h-12 w-12 text-red-500 mb-4" />
              <h2 className="text-xl font-semibold mb-2">Product Limit Reached</h2>
              <p className="text-muted-foreground mb-6 max-w-md">
                You&apos;ve reached your product limit for your current subscription plan.
                Please upgrade your subscription to add more products.
              </p>
              <div className="flex flex-col sm:flex-row gap-3">
                <Button variant="outline" onClick={() => router.push("/dashboard/brand/products")}>
                  Go to Products
                </Button>
                <Button onClick={() => router.push("/dashboard/brand/subscription")}>
                  <ArrowUpCircle className="h-4 w-4 mr-2" />
                  Upgrade Subscription
                </Button>
              </div>
            </div>
          </CardContent>
        </Card>
      </div>
    );
  }

  return (
    <div className="max-w-3xl mx-auto py-8 px-4 md:px-0">
      <Button variant="link" onClick={() => router.back()} className="mb-4 p-0">
        <ArrowLeft className="h-4 w-4 mr-2" />
        Back to Products
      </Button>

      <Card className="shadow-md">
        <CardHeader className="bg-slate-50 border-b pb-4">
          <CardTitle className="text-2xl">Add a New Product</CardTitle>
          <CardDescription className="mt-1">
            Enter your product details to start promoting it through AdMesh
          </CardDescription>
        </CardHeader>

        {step === 1 && (
          <AnimatedContainer>
            <CardContent className="pt-6 space-y-6">
              <FormField>
                <Label htmlFor="website" className="text-sm font-medium">Website</Label>
                <div className="flex gap-2">
                  <Input
                    id="website"
                    placeholder="yourbrand.com"
                    value={product.website}
                    onChange={(e) => handleChange("website", e.target.value)}
                    className={`transition-all duration-200 focus:ring-2 focus:ring-primary/20 ${errors.website ? 'border-red-500' : ''}`}
                  />
                  <Button
                    onClick={fetchWebsiteInfo}
                    disabled={fetchingWebsiteInfo}
                    variant="outline"
                    className="whitespace-nowrap"
                  >
                    {fetchingWebsiteInfo ? (
                      <>
                        <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                        Fetching...
                      </>
                    ) : (
                      "Fetch Info"
                    )}
                  </Button>
                </div>
                {websiteError && (
                  <p className="text-xs text-red-500 mt-1">{websiteError}</p>
                )}
                <p className="text-xs text-muted-foreground mt-1">
                  Enter your website URL to automatically fetch product details
                </p>
              </FormField>

              {showAllFields && (
                <motion.div
                  initial={{ opacity: 0, height: 0 }}
                  animate={{ opacity: 1, height: "auto" }}
                  transition={{ duration: 0.3 }}
                  className="space-y-6"
                >
                  <FormField>
                    <Label htmlFor="title" className="text-sm font-medium">Product Title</Label>
                    <Input
                      id="title"
                      placeholder="Your Amazing Product"
                      value={product.title}
                      onChange={(e) => handleChange("title", e.target.value)}
                      className={`transition-all duration-200 focus:ring-2 focus:ring-primary/20 ${errors.title ? 'border-red-500' : ''}`}
                    />
                    {errors.title && (
                      <p className="text-xs text-red-500 mt-1">{errors.title}</p>
                    )}
                  </FormField>

                  <FormField>
                    <Label htmlFor="url" className="text-sm font-medium">Product URL</Label>
                    <Input
                      id="url"
                      placeholder="https://yourbrand.com/product"
                      value={product.url}
                      onChange={(e) => handleChange("url", e.target.value)}
                      className={`transition-all duration-200 focus:ring-2 focus:ring-primary/20 ${errors.url ? 'border-red-500' : ''}`}
                    />
                    {errors.url && (
                      <p className="text-xs text-red-500 mt-1">{errors.url}</p>
                    )}
                  </FormField>

                  <FormField>
                    <Label htmlFor="description" className="text-sm font-medium">Description</Label>
                    <Textarea
                      id="description"
                      placeholder="Describe what your product does and its key benefits"
                      rows={3}
                      value={product.description}
                      onChange={(e) => handleChange("description", e.target.value)}
                      className={`resize-none transition-all duration-200 focus:ring-2 focus:ring-primary/20 ${errors.description ? 'border-red-500' : ''}`}
                    />
                    {errors.description && (
                      <p className="text-xs text-red-500 mt-1">{errors.description}</p>
                    )}
                  </FormField>

                  <FormField>
                    <Label htmlFor="categories" className="text-sm font-medium">Categories (comma-separated, max 3)</Label>
                    <Input
                      id="categories"
                      placeholder="e.g. Marketing, Development Tools, SaaS"
                      value={product.categories.join(", ")}
                      onChange={(e) => {
                        const categories = e.target.value.split(",").map(c => c.trim()).filter(c => c.length > 0);
                        handleChange("categories", categories.slice(0, 3)); // Limit to 3 categories
                      }}
                      className={`transition-all duration-200 focus:ring-2 focus:ring-primary/20 ${errors.categories ? 'border-red-500' : ''}`}
                    />
                    {errors.categories && (
                      <p className="text-xs text-red-500 mt-1">{errors.categories}</p>
                    )}
                    <p className="text-xs text-muted-foreground mt-1">
                      Enter up to 3 categories separated by commas
                    </p>
                  </FormField>

                  <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                    <FormField>
                      <Label htmlFor="pricing_url" className="text-sm font-medium">Pricing URL (optional)</Label>
                      <Input
                        id="pricing_url"
                        placeholder="https://yourbrand.com/pricing"
                        value={product.pricing_url}
                        onChange={(e) => handleChange("pricing_url", e.target.value)}
                        className="transition-all duration-200 focus:ring-2 focus:ring-primary/20"
                      />
                    </FormField>

                    <FormField>
                      <Label htmlFor="audience_segment" className="text-sm font-medium">Target Audience</Label>
                      <Input
                        id="audience_segment"
                        placeholder="e.g. Startups, Agencies, Developers"
                        value={product.audience_segment}
                        onChange={(e) => handleChange("audience_segment", e.target.value)}
                        className="transition-all duration-200 focus:ring-2 focus:ring-primary/20"
                      />
                    </FormField>
                  </div>

                  <FormField>
                    <Label htmlFor="keywords" className="text-sm font-medium">Keywords (comma-separated)</Label>
                    <Input
                      id="keywords"
                      placeholder="productivity, tool, analytics"
                      value={product.keywords.join(", ")}
                      onChange={(e) => {
                        const keywords = e.target.value.split(",").map(k => k.trim()).filter(k => k.length > 0);
                        handleChange("keywords", keywords);
                      }}
                      className="transition-all duration-200 focus:ring-2 focus:ring-primary/20"
                    />
                  </FormField>

                  <FormField>
                    <Label htmlFor="integration_list" className="text-sm font-medium">Integrations (comma-separated, optional)</Label>
                    <Input
                      id="integration_list"
                      placeholder="Slack, Zapier, Stripe"
                      value={product.integration_list.join(", ")}
                      onChange={(e) => {
                        const integrations = e.target.value.split(",").map(i => i.trim()).filter(i => i.length > 0);
                        handleChange("integration_list", integrations);
                      }}
                      className="transition-all duration-200 focus:ring-2 focus:ring-primary/20"
                    />
                    <p className="text-xs text-muted-foreground mt-1">
                      List the tools and services your product integrates with
                    </p>
                  </FormField>


                </motion.div>
              )}
            </CardContent>
          </AnimatedContainer>
        )}

        <CardFooter className="justify-end gap-2 border-t p-6 bg-gray-50">
          <Button variant="outline" onClick={() => router.back()} disabled={loading}>
            Cancel
          </Button>
          <Button
            disabled={loading || !showAllFields}
            onClick={handleSubmit}
            className="px-8"
          >
            {loading ? (
              <>
                <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                Creating...
              </>
            ) : (
              "Create Product"
            )}
          </Button>
        </CardFooter>
      </Card>
    </div>
  );
}
