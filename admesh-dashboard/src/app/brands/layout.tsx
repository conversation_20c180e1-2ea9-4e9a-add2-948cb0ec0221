import { Metadata } from "next";
import { generateMetadata } from "../metadata";

// Brands page specific metadata - serves brands content at /brands
export const metadata: Metadata = generateMetadata(
  "AdMesh for Brands - AI Marketing Platform & Performance Marketing AI",
  "AdMesh is the leading AI marketing platform that connects brands with high-intent users through intelligent AI agents. Our performance marketing AI and automated marketing solutions reach customers the moment they search. Revolutionary AI-powered marketing for brands. Official AdMesh platform for brands. Get started with useadmesh today.",
  "/brands", // Path - canonical points to /brands since this page serves content here
  undefined // Let the generateMetadata function create the static OG image URL
);

export default function BrandsLayout({
  children,
}: {
  children: React.ReactNode;
}) {
  return (
    <>
      {/* Brands-specific structured data */}
      <script
        type="application/ld+json"
        dangerouslySetInnerHTML={{
          __html: JSON.stringify({
            "@context": "https://schema.org",
            "@type": "WebPage",
            name: "AdMesh for Brands - AI Marketing Platform",
            description: "AdMesh is the leading AI marketing platform that connects brands with high-intent users through intelligent AI agents. Revolutionary AI-powered marketing for brands.",
            url: "https://useadmesh.com/brands",
            mainEntity: {
              "@type": "SoftwareApplication",
              name: "AdMesh AI Marketing Platform",
              applicationCategory: "BusinessApplication",
              operatingSystem: "Web",
              offers: {
                "@type": "Offer",
                price: "0",
                priceCurrency: "USD",
                description: "Free tier available with premium plans"
              },
              featureList: [
                "AI-powered marketing automation",
                "Performance marketing AI",
                "Real-time customer targeting",
                "Intelligent agent recommendations",
                "Advanced analytics and reporting"
              ],
              audience: {
                "@type": "Audience",
                audienceType: "Brands and Marketing Teams"
              }
            },
            breadcrumb: {
              "@type": "BreadcrumbList",
              itemListElement: [
                {
                  "@type": "ListItem",
                  position: 1,
                  name: "Home",
                  item: "https://useadmesh.com"
                },
                {
                  "@type": "ListItem",
                  position: 2,
                  name: "Brands",
                  item: "https://useadmesh.com/brands"
                }
              ]
            }
          })
        }}
      />
      {children}
    </>
  );
}
