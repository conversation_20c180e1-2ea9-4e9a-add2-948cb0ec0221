// Default metadata for the entire site
export const metadata = {
  // Basic metadata - optimized for brand name searches
  title: "AdMesh – Promote your products inside AI",
  description: "AdMesh is the leading AI marketing platform that places your products directly inside real conversations happening across AI tools. Our performance marketing AI connects brands with high-intent users the moment they search. Revolutionary AI-powered marketing for the future. Official AdMesh website.",
  keywords: "AdMesh, useadmesh, admesh, AI tools, product discovery, agent, rewards, monetized intent, AI advertising, product placement, AI agents, AI marketing, performance marketing AI, AI-powered marketing, automated marketing, intelligent marketing, AI marketing platform, performance marketing tools, AI advertising platform, marketing automation AI, AI marketing solutions, digital marketing AI, AI performance optimization, marketing AI technology, AI-driven marketing, smart marketing AI",

  // Icons - comprehensive favicon setup
  icons: {
    icon: [
      { url: "/favicon.ico", type: "image/x-icon" },
      { url: "/favicon.png", sizes: "32x32", type: "image/png" },
      { url: "/favicon-16.png", sizes: "16x16", type: "image/png" }
    ],
    shortcut: "/favicon.ico",
    apple: [
      { url: "/favicon.png", sizes: "180x180", type: "image/png" }
    ],
  },

  // Web App Manifest
  manifest: "/site.webmanifest",

  // Open Graph metadata for social sharing
  openGraph: {
    type: "website",
    locale: "en_US",
    url: "https://useadmesh.com",
    siteName: "AdMesh",
    title: "AdMesh – Promote your products inside AI",
    description: "AdMesh is the leading AI marketing platform that places your products directly inside real conversations happening across AI tools. Our performance marketing AI connects brands with high-intent users the moment they search. Revolutionary AI-powered marketing for the future. Official AdMesh website.",
    images: [
      {
        url: "https://useadmesh.com/og-images/root-og.png", // Use static OG image for root
        width: 1200,
        height: 630,
        alt: "AdMesh - Promote your products inside AI",
      },
    ],
  },

  // Twitter card metadata
  twitter: {
    card: "summary_large_image",
    title: "AdMesh – Promote your products inside AI",
    description: "AdMesh is the leading AI marketing platform that places your products directly inside real conversations happening across AI tools. Our performance marketing AI connects brands with high-intent users the moment they search. Revolutionary AI-powered marketing for the future. Official AdMesh website.",
    images: ["https://useadmesh.com/og-images/root-og.png"], // Use static OG image for root
    creator: "@useadmesh",
    site: "@useadmesh",
  },

  // Verification for search engines
  verification: {
    google: "google-site-verification-code", // Replace with actual verification code
  },

  // Additional metadata for brand recognition
  other: {
    "brand": "AdMesh",
    "application-name": "AdMesh",
    "apple-mobile-web-app-title": "AdMesh",
    "msapplication-TileColor": "#000000",
    "theme-color": "#000000",
  },

  // Canonical URL
  alternates: {
    canonical: "https://useadmesh.com",
  },

  // Robots directives
  robots: {
    index: true,
    follow: true,
    googleBot: "index, follow, max-image-preview:large, max-snippet:-1",
  },
};

// Page-specific metadata generator
export function generateMetadata(
  title?: string,
  description?: string,
  path?: string,
  image?: string
  // Removed useStaticImage parameter as we're always using static images now
) {
  const pageTitle = title ? `${title} | AdMesh` : metadata.title;
  const pageDescription = description || metadata.description;
  const pageUrl = path ? `https://useadmesh.com${path}` : "https://useadmesh.com";

  // Generate OG image URL
  let pageImage;

  // Force using static OG images for production
  if (path) {
    // Always use static OG image based on path
    const routeName = path === "/" ? "root" : path.replace(/^\//, '').split('/')[0];
    pageImage = `https://useadmesh.com/og-images/${routeName}-og.png`;

    // Log for debugging
    console.log(`Using static OG image for path ${path}: ${pageImage}`);
  } else if (image) {
    // Use provided image
    pageImage = image;
    console.log(`Using provided image: ${pageImage}`);
  } else {
    // Use standard OG image with text as fallback
    pageImage = `https://useadmesh.com/api/og?title=${encodeURIComponent(pageTitle)}&description=${encodeURIComponent(pageDescription)}${path ? `&type=${path.replace(/^\//, '').split('/')[0] || 'default'}` : ''}&mode=dark`;
    console.log(`Using dynamic OG image: ${pageImage}`);
  }

  return {
    title: pageTitle,
    description: pageDescription,
    openGraph: {
      // Explicitly set all properties to avoid inheriting defaults
      type: metadata.openGraph.type,
      locale: metadata.openGraph.locale,
      siteName: metadata.openGraph.siteName,
      title: pageTitle,
      description: pageDescription,
      url: pageUrl,
      images: [
        {
          url: pageImage,
          width: 1200,
          height: 630,
          alt: pageTitle,
        },
      ],
    },
    twitter: {
      card: metadata.twitter.card,
      creator: metadata.twitter.creator,
      title: pageTitle,
      description: pageDescription,
      images: [pageImage],
    },
    alternates: {
      canonical: pageUrl,
    },
  };
}
