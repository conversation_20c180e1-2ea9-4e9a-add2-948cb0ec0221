"use client";

import Link from "next/link";
import { useEffect, useState } from "react";
import { Button } from "@/components/ui/button";
import { Menu, X } from "lucide-react";
import { onAuthStateChanged, signOut, User } from "firebase/auth";
import { auth } from "@/lib/firebase";
import AuthModal from "@/components/AuthModal";
import Image from "next/image";
import { usePathname } from "next/navigation";

interface NavItem {
  label: string;
  href: string;
  isScroll?: boolean;
}

export default function Navbar() {
  const [isMenuOpen, setIsMenuOpen] = useState(false);
  const [scrolled, setScrolled] = useState(false);
  const [user, setUser] = useState<User | null>(null);
  const [authOpen, setAuthOpen] = useState(false);
  const pathname = usePathname();

  const toggleMenu = () => setIsMenuOpen(!isMenuOpen);
  const handleLogout = async () => {
    await signOut(auth);
    setIsMenuOpen(false);
  };

  useEffect(() => {
    const unsubscribe = onAuthStateChanged(auth, (currentUser) => {
      setUser(currentUser);
    });
    return () => unsubscribe();
  }, []);

  useEffect(() => {
    const onScroll = () => setScrolled(window.scrollY > 10);
    window.addEventListener("scroll", onScroll);
    return () => window.removeEventListener("scroll", onScroll);
  }, []);

  // Define all possible nav items
  const allNavItems: NavItem[] = [
    { label: "Brands", href: "/brands" },
    { label: "Agents", href: "/agents" },
    { label: "Users", href: "/users" },
    { label: "Pioneer Program", href: "/users#agent-pioneer-section", isScroll: true },
    { label: "Pricing", href: "/brands#pricing", isScroll: true },
  ];

  // Filter nav items based on current path
  const navItems: NavItem[] = allNavItems.filter(item => {
    // Hide "Pioneer Program" when on brands, agents, or users paths
    if (item.label === "Pioneer Program" && (
      pathname.startsWith("/brands") ||
      pathname.startsWith("/agents") ||
      pathname.startsWith("/users")
    )) {
      return false;
    }

    // Show "Pricing" only when on brands path
    if (item.label === "Pricing" && !pathname.startsWith("/brands")) {
      return false;
    }

    return true;
  });

  return (
    <>
      <header
        className={`fixed top-0 z-50 w-full transition-all duration-300 ${
          scrolled
            ? "bg-background/80 backdrop-blur-md border-b border-border shadow-sm"
            : "bg-transparent border-transparent"
        }`}
      >
        <div className="container mx-auto flex h-16 items-center justify-between px-4 sm:px-6">
          {/* Logo */}
          <Link href="/" className="flex items-center">
            <Image src="/logo.svg" alt="AdMesh Logo" width={48} height={48} />
            <span className="text-lg font-bold tracking-tight font-inter text-foreground">AdMesh</span>
          </Link>

          {/* Desktop Nav */}
          <nav className="hidden md:flex items-center space-x-8">
            {navItems.map((link) => (
              link.isScroll ? (
                <a
                  key={link.href}
                  href={link.href}
                  className="text-sm font-medium hover:text-primary transition-colors cursor-pointer"
                  onClick={(e) => {
                    e.preventDefault();
                    const parts = link.href.split('#');
                    const path = parts[0];
                    const sectionId = parts[1];

                    // If we're already on the correct path, just scroll
                    if (pathname === path || (path === '/' && pathname === '') ||
                        (path === '/brands' && pathname === '/brands') ||
                        (path === '/users' && pathname === '/users')) {
                      const section = document.getElementById(sectionId);
                      if (section) {
                        section.scrollIntoView({ behavior: 'smooth' });
                      }
                    } else {
                      // Otherwise, navigate to the path with the hash
                      window.location.href = link.href;
                    }
                  }}
                >
                  {link.label}
                </a>
              ) : (
                <Link
                  key={link.href}
                  href={link.href}
                  className="text-sm font-medium hover:text-primary transition-colors"
                >
                  {link.label}
                </Link>
              )
            ))}
          </nav>

          {/* Right Side CTA */}
          <div className="flex items-center gap-4">
            {user ? (
              <Link href="/dashboard">
                <Button
                  variant="default"
                  className="rounded-md bg-primary text-primary-foreground hover:bg-primary/90 transition"
                >
                  Dashboard
                </Button>
              </Link>
            ) : (
              <Button
                variant="default"
                className="rounded-md bg-primary text-primary-foreground hover:bg-primary/90 transition"
                onClick={() => setAuthOpen(true)}
              >
                Get Started
              </Button>
            )}

            {/* Mobile Menu Button */}
            <button
              className="md:hidden p-2 -mr-2 focus:outline-none"
              onClick={toggleMenu}
              aria-label="Toggle navigation menu"
            >
              {isMenuOpen ? <X size={24} /> : <Menu size={24} />}
            </button>
          </div>
        </div>

        {/* Mobile Menu */}
        {isMenuOpen && (
          <div className="md:hidden bg-background border-b border-border">
            <div className="container mx-auto px-4 py-4 flex flex-col space-y-4">
              {navItems.map((item) => (
                item.isScroll ? (
                  <a
                    key={item.href}
                    href={item.href}
                    className="text-sm font-medium py-2 hover:text-primary transition-colors cursor-pointer"
                    onClick={(e) => {
                      e.preventDefault();
                      setIsMenuOpen(false);
                      const parts = item.href.split('#');
                      const path = parts[0];
                      const sectionId = parts[1];

                      // If we're already on the correct path, just scroll
                      if (pathname === path || (path === '/' && pathname === '') ||
                          (path === '/brands' && pathname === '/brands') ||
                          (path === '/users' && pathname === '/users')) {
                        const section = document.getElementById(sectionId);
                        if (section) {
                          section.scrollIntoView({ behavior: 'smooth' });
                        }
                      } else {
                        // Otherwise, navigate to the path with the hash
                        window.location.href = item.href;
                      }
                    }}
                  >
                    {item.label}
                  </a>
                ) : (
                  <Link
                    key={item.href}
                    href={item.href}
                    className="text-sm font-medium py-2 hover:text-primary transition-colors"
                    onClick={() => setIsMenuOpen(false)}
                  >
                    {item.label}
                  </Link>
                )
              ))}

              <div className="h-px w-full bg-border my-2" />

              {user ? (
                <>
                  <Link
                    href="/dashboard"
                    className="text-sm font-medium py-2 hover:text-primary transition-colors"
                    onClick={() => setIsMenuOpen(false)}
                  >
                    Dashboard
                  </Link>
                  <button
                    onClick={handleLogout}
                    className="text-sm font-medium py-2 text-red-600 hover:text-red-800 transition-colors text-left"
                  >
                    Logout
                  </button>
                </>
              ) : (
                <Button
                  variant="default"
                  className="text-sm font-medium py-2 hover:text-primary transition-colors"
                  onClick={() => {
                    setAuthOpen(true);
                    setIsMenuOpen(false);
                  }}
                >
                  Get Started
                </Button>
              )}
            </div>
          </div>
        )}
      </header>

      <AuthModal open={authOpen} onClose={() => setAuthOpen(false)} />
    </>
  );
}
