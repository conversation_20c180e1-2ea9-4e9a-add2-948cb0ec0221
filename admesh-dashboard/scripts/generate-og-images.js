/**
 * <PERSON><PERSON><PERSON> to generate static OG images for different routes
 *
 * This script uses P<PERSON>peteer to capture screenshots of different pages
 * and saves them as static OG images in the public/og-images directory.
 *
 * Usage:
 * node scripts/generate-og-images.js
 */

const puppeteer = require('puppeteer');
const fs = require('fs');
const path = require('path');
const { execSync } = require('child_process');

// Configuration
const OG_IMAGES_DIR = path.join(__dirname, '../public/og-images');
const BASE_URL = process.env.NEXT_PUBLIC_SITE_URL || 'http://localhost:3000';
const PRODUCTION_URL = 'https://useadmesh.com'; // Updated to use canonical non-www domain
const VIEWPORT = { width: 1200, height: 630 };

// Routes to capture
const ROUTES = [
  { path: '/brands', name: 'brands' },
  { path: '/agents', name: 'agents' },
  { path: '/', name: 'root' },
  { path: '/user', name: 'user' },
];

// Ensure the OG images directory exists
if (!fs.existsSync(OG_IMAGES_DIR)) {
  fs.mkdirSync(OG_IMAGES_DIR, { recursive: true });
  console.log(`Created directory: ${OG_IMAGES_DIR}`);
}

/**
 * Captures a screenshot of a page and saves it as an OG image
 *
 * @param {string} url The URL to capture
 * @param {string} outputPath The path to save the screenshot
 * @param {object} viewport The viewport dimensions
 */
async function captureScreenshot(url, outputPath, viewport) {
  console.log(`Capturing screenshot of ${url}...`);

  const browser = await puppeteer.launch({
    headless: 'new',
    args: ['--no-sandbox', '--disable-setuid-sandbox'],
  });

  try {
    const page = await browser.newPage();

    // Set viewport
    await page.setViewport(viewport);

    // Navigate to URL
    await page.goto(url, { waitUntil: 'networkidle2' });

    // Wait for any animations to complete
    await page.waitForTimeout(1000);

    // Take screenshot
    await page.screenshot({ path: outputPath });

    console.log(`Screenshot saved to ${outputPath}`);
  } catch (error) {
    console.error(`Error capturing screenshot of ${url}:`, error);
  } finally {
    await browser.close();
  }
}

/**
 * Main function to generate all OG images
 */
async function generateOGImages() {
  console.log('Generating OG images...');

  // Check if the development server is running
  let serverRunning = false;
  try {
    execSync('curl -s http://localhost:3000 > /dev/null');
    serverRunning = true;
  } catch (error) {
    console.log('Development server is not running. Starting it...');
    // Start the development server in the background
    execSync('npm run dev &', { stdio: 'inherit' });

    // Wait for the server to start
    console.log('Waiting for the server to start...');
    await new Promise(resolve => setTimeout(resolve, 5000));
  }

  // Generate OG images for each route
  for (const route of ROUTES) {
    const url = `${BASE_URL}${route.path}`;
    const outputPath = path.join(OG_IMAGES_DIR, `${route.name}-og.png`);

    await captureScreenshot(url, outputPath, VIEWPORT);
  }

  console.log('OG images generated successfully!');

  // If we started the server, kill it
  if (!serverRunning) {
    console.log('Stopping the development server...');
    execSync('pkill -f "next dev"');
  }
}

// Run the script
generateOGImages().catch(error => {
  console.error('Error generating OG images:', error);
  process.exit(1);
});
